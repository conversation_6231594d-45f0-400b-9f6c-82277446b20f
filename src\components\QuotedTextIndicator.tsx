import React from 'react';
import { X } from 'lucide-react';

interface QuotedTextIndicatorProps {
  text: string;
  onClear: () => void;
}

const QuotedTextIndicator: React.FC<QuotedTextIndicatorProps> = ({ text, onClear }) => {
  // Truncate text if it's too long
  const displayText = text.length > 50 ? `${text.substring(0, 50)}...` : text;
  
  return (
    <div className="flex items-center gap-2 px-3 py-1.5 bg-gray-100 rounded-md mb-2 text-sm">
      <div className="flex-1 text-gray-600 overflow-hidden text-ellipsis whitespace-nowrap">
        <span className="font-medium text-gray-700">Replying to:</span> {displayText}
      </div>
      <button 
        onClick={onClear}
        className="text-gray-400 hover:text-gray-600 transition-colors"
        title="Clear quoted text"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  );
};

export default QuotedTextIndicator;
