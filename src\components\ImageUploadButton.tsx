
import React, { useRef } from "react";
import { cn } from "@/lib/utils";

interface ImageUploadButtonProps {
  onImageSelect: (file: File) => void;
  disabled?: boolean;
  className?: string;
  children: React.ReactNode;
}

const ImageUploadButton = ({ 
  onImageSelect, 
  disabled = false, 
  className,
  children 
}: ImageUploadButtonProps) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleClick = () => {
    inputRef.current?.click();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onImageSelect(file);
      // Reset input so the same file can be selected again
      if (inputRef.current) inputRef.current.value = '';
    }
  };

  return (
    <>
      <button
        type="button"
        disabled={disabled}
        className={cn("flex items-center justify-center transition-colors", className)}
        onClick={handleClick}
        title="Upload image"
      >
        {children}
      </button>
      <input
        ref={inputRef}
        type="file"
        accept="image/*"
        onChange={handleChange}
        className="hidden"
        disabled={disabled}
      />
    </>
  );
};

export default ImageUploadButton;
