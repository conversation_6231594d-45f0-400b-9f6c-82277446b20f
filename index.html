<!DOCTYPE html>
<html lang="en">
  <head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-L6M6K6WLX0"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-L6M6K6WLX0');
    </script>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="robots" content="noindex, nofollow" />
    <meta name="googlebot" content="noindex, nofollow" />
    <title>UniAI - Free AI Assistant for University Students</title>
    <meta name="description" content="Access powerful AI assistance for free as a university student. Get academic help, research assistance, assignment support, and exam preparation - all at zero cost." />
    <meta name="author" content="UniAI" />
    <meta name="keywords" content="free AI, university students, academic AI, university homework, research assistant, assignment help, academic problem-solving, university exams, student AI assistant" />

    <meta property="og:title" content="UniAI - Free AI for University Students" />
    <meta property="og:description" content="Struggling with university assignments? UniAI offers free AI assistance optimized for university students. Get instant help with research, assignments, and exam preparation across all disciplines." />
    <meta property="og:type" content="website" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@uniai" />
    <meta name="twitter:title" content="UniAI - Free AI for University Students" />
    <meta name="twitter:description" content="Free AI assistance optimized for university students. Get help with research, assignments, and exam preparation across all disciplines." />
  </head>

  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
