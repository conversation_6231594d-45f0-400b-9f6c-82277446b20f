import React, { useRef } from "react";
import { cn } from "@/lib/utils";

interface FileUploadButtonProps {
  onFileSelect: (file: File) => void;
  disabled?: boolean;
  className?: string;
  children: React.ReactNode;
  accept?: string;
  title?: string;
}

const FileUploadButton = ({ 
  onFileSelect, 
  disabled = false, 
  className,
  children,
  accept = "image/*,application/pdf",
  title = "Upload file"
}: FileUploadButtonProps) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleClick = () => {
    inputRef.current?.click();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onFileSelect(file);
      // Reset input so the same file can be selected again
      if (inputRef.current) inputRef.current.value = '';
    }
  };

  return (
    <>
      <button
        type="button"
        disabled={disabled}
        className={cn("flex items-center justify-center transition-colors", className)}
        onClick={handleClick}
        title={title}
      >
        {children}
      </button>
      <input
        ref={inputRef}
        type="file"
        accept={accept}
        onChange={handleChange}
        className="hidden"
        disabled={disabled}
      />
    </>
  );
};

export default FileUploadButton;
