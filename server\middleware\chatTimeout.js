import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Store last chat timestamps for each client
const lastChatTimestamps = {};

// Chat timeout middleware
const chatTimeoutMiddleware = (req, res, next) => {
  const clientIp = req.ip;
  const now = Date.now();
  const minInterval = parseInt(process.env.MIN_CHAT_INTERVAL_MS) || 3000; // Default: 3 seconds
  
  if (lastChatTimestamps[clientIp] && (now - lastChatTimestamps[clientIp]) < minInterval) {
    return res.status(429).json({
      status: 429,
      message: `Please wait ${Math.ceil((lastChatTimestamps[clientIp] + minInterval - now) / 1000)} seconds before sending another message.`
    });
  }
  
  lastChatTimestamps[clientIp] = now;
  next();
};

// Cleanup old timestamps periodically (every hour)
setInterval(() => {
  const now = Date.now();
  const oneHour = 60 * 60 * 1000;
  
  Object.keys(lastChatTimestamps).forEach(ip => {
    if (now - lastChatTimestamps[ip] > oneHour) {
      delete lastChatTimestamps[ip];
    }
  });
}, 60 * 60 * 1000);

export default chatTimeoutMiddleware;
