import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create rate limiter middleware
const rateLimiter = rateLimit({
  windowMs: parseInt(process.env.WINDOW_MS) || 3600000, // Default: 1 hour
  max: parseInt(process.env.MAX_REQUESTS) || 50, // Default: 50 requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    status: 429,
    message: 'Too many requests, please try again later.'
  }
});

export default rateLimiter;
