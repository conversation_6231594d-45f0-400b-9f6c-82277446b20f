
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221 83% 53%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-[#f7f7f8] text-foreground font-sans;
  }

  /* Improved scrollbar styling */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full hover:bg-gray-400 transition-colors;
  }
}

/* Animation improvements */
.message-appear {
  animation: fade-in 0.3s ease-out;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Markdown styling with colors and better formatting */

/* Paragraphs and basic text */
.markdown-content p {
  @apply my-3 leading-relaxed;
}

/* Headings with improved hierarchy and spacing */
.markdown-content h1, .markdown-content h2, .markdown-content h3,
.markdown-content h4, .markdown-content h5, .markdown-content h6 {
  @apply font-semibold my-4 text-gray-900;
}

.markdown-content h1 {
  @apply text-2xl border-b border-gray-200 pb-2 mb-4;
}

.markdown-content h2 {
  @apply text-xl border-b border-gray-100 pb-1 mb-3;
}

.markdown-content h3 {
  @apply text-lg;
}

.markdown-content h4 {
  @apply text-base text-gray-800;
}

/* Lists with better spacing and bullets */
.markdown-content ul, .markdown-content ol {
  @apply pl-6 my-4 space-y-1;
}

.markdown-content ul {
  @apply list-disc;
}

.markdown-content ol {
  @apply list-decimal;
}

.markdown-content li > ul, .markdown-content li > ol {
  @apply my-1;
}

/* Links with better hover effects */
.markdown-content a {
  @apply text-blue-600 underline decoration-blue-300 underline-offset-2 hover:text-blue-800 hover:decoration-blue-500 transition-colors;
}

/* Blockquotes with improved styling */
.markdown-content blockquote {
  @apply border-l-4 border-gray-300 pl-4 py-1 my-4 bg-gray-50 rounded-r-md text-gray-700 italic;
}

/* Tables with better styling */
.markdown-content .table-container {
  @apply my-4 overflow-x-auto rounded-md border border-gray-200 shadow-sm;
}

.markdown-content table {
  @apply w-full border-collapse;
}

.markdown-content thead {
  @apply bg-gray-100;
}

.markdown-content th {
  @apply border border-gray-200 p-2 text-left font-semibold text-gray-700;
}

.markdown-content td {
  @apply border border-gray-200 p-2 text-gray-800;
}

.markdown-content tr:nth-child(even) {
  @apply bg-gray-50;
}

/* Code blocks with syntax highlighting */
.markdown-content .code-block-wrapper {
  @apply my-4 rounded-md overflow-hidden border border-gray-200 shadow-sm;
}

.markdown-content .code-block-header {
  @apply flex justify-between items-center px-4 py-2 bg-gray-800 text-white text-sm;
}

.markdown-content .code-language {
  @apply font-medium uppercase;
}

.markdown-content .copy-button {
  @apply px-2 py-1 rounded text-xs bg-gray-700 hover:bg-gray-600 border border-gray-600 transition-colors;
}

.markdown-content .copy-button.copied {
  @apply bg-green-600 text-white border-green-500;
}

/* Inline code styling */
.markdown-content :not(pre) > code {
  @apply font-mono text-sm bg-gray-100 px-1.5 py-0.5 rounded text-gray-800;
}

/* Override SyntaxHighlighter styles */
.markdown-content pre {
  @apply m-0 text-sm;
}

/* Fix for SyntaxHighlighter */
.markdown-content pre.prism-code {
  @apply bg-gray-900 text-gray-100;
}

.markdown-content pre.prism-code span {
  background: transparent !important;
}

/* Callouts/admonitions styling */
.markdown-content .callout {
  @apply my-4 rounded-md overflow-hidden border shadow-sm;
}

.markdown-content .callout-header {
  @apply flex items-center gap-2 px-4 py-2 font-medium;
}

.markdown-content .callout-content {
  @apply px-4 py-3;
}

/* Note callout - blue */
.markdown-content .callout-note {
  @apply border-blue-200 bg-blue-50;
}

.markdown-content .callout-note .callout-header {
  @apply bg-blue-100 text-blue-800;
}

/* Warning callout - amber/orange */
.markdown-content .callout-warning {
  @apply border-amber-200 bg-amber-50;
}

.markdown-content .callout-warning .callout-header {
  @apply bg-amber-100 text-amber-800;
}

/* Tip callout - green */
.markdown-content .callout-tip {
  @apply border-green-200 bg-green-50;
}

.markdown-content .callout-tip .callout-header {
  @apply bg-green-100 text-green-800;
}

/* Important callout - purple */
.markdown-content .callout-important {
  @apply border-purple-200 bg-purple-50;
}

.markdown-content .callout-important .callout-header {
  @apply bg-purple-100 text-purple-800;
}

/* Custom welcome card styling */
.welcome-card {
  @apply transition-all duration-300;
}

/* Custom resize handle styles */
.resize-handle {
  @apply cursor-ns-resize transition-colors;
}

.resize-handle:hover {
  @apply bg-gray-100;
}
