import React, { useEffect, useRef } from 'react';
import { MessageSquareQuote } from 'lucide-react';

interface TextSelectionPopupProps {
  position: { x: number; y: number } | null;
  onReply: () => void;
  visible: boolean;
}

const TextSelectionPopup: React.FC<TextSelectionPopupProps> = ({
  position,
  onReply,
  visible
}) => {
  const popupRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Ensure the popup stays within viewport bounds
    if (popupRef.current && position) {
      const rect = popupRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      // Adjust x position if popup would go off-screen
      let adjustedX = position.x;
      if (position.x + rect.width > viewportWidth) {
        adjustedX = viewportWidth - rect.width - 10;
      }
      
      // Adjust y position if popup would go off-screen
      let adjustedY = position.y;
      if (position.y + rect.height > viewportHeight) {
        adjustedY = position.y - rect.height - 10;
      }
      
      if (popupRef.current) {
        popupRef.current.style.left = `${adjustedX}px`;
        popupRef.current.style.top = `${adjustedY}px`;
      }
    }
  }, [position]);

  if (!visible || !position) return null;

  return (
    <div
      ref={popupRef}
      className="fixed z-50 bg-white rounded-full shadow-md border border-gray-200 transition-opacity duration-200 opacity-100"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
      }}
    >
      <button
        onClick={onReply}
        className="flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-full transition-colors"
        title="Reply to selected text"
      >
        <MessageSquareQuote className="h-4 w-4" />
        <span>Reply</span>
      </button>
    </div>
  );
};

export default TextSelectionPopup;
