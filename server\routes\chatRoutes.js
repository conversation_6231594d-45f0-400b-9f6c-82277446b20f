import express from 'express';
import dotenv from 'dotenv';
import chatTimeoutMiddleware from '../middleware/chatTimeout.js';
import { universitySystemMessage } from '../middleware/systemMessageMiddleware.js';

// Dynamic import for pdf-parse to avoid initialization issues
let pdfParse = null;
async function loadPdfParse() {
  if (!pdfParse) {
    try {
      const module = await import('pdf-parse');
      pdfParse = module.default;
    } catch (error) {
      console.error('Failed to load pdf-parse:', error);
      throw new Error('PDF parsing not available');
    }
  }
  return pdfParse;
}

// Load environment variables
dotenv.config();

const router = express.Router();

// Apply chat timeout middleware
router.use(chatTimeoutMiddleware);

// Helper function to process PDF files
async function processPdfFile(file) {
  try {
    // Convert base64 to buffer if needed
    let buffer;
    if (file.content && file.content.startsWith('data:application/pdf;base64,')) {
      const base64Data = file.content.replace('data:application/pdf;base64,', '');
      buffer = Buffer.from(base64Data, 'base64');
    } else {
      throw new Error('Invalid PDF format');
    }

    // Load pdf-parse dynamically and extract text
    const pdfParseFunc = await loadPdfParse();
    const data = await pdfParseFunc(buffer);
    return data.text;
  } catch (error) {
    console.error('Error processing PDF:', error);
    throw new Error('Failed to extract text from PDF');
  }
}

// Add a test endpoint
router.get('/test', (req, res) => {
  res.json({ status: 'API is working' });
});

// Chat completion endpoint
router.post('/', async (req, res) => {
  try {
    const { messages } = req.body;

    if (!messages || !Array.isArray(messages)) {
      return res.status(400).json({ error: 'Invalid request format' });
    }

    const openaiApiKey = process.env.OPENAI_API_KEY;
    if (!openaiApiKey) {
      console.error('OpenAI API key is not configured');
      return res.status(500).json({ error: 'OpenAI API key not configured' });
    }


    // Filter out any existing system messages from the client
    const userMessages = messages.filter(msg => msg.role !== 'system');

    // Process PDF files and extract text
    const processedMessages = await Promise.all(userMessages.map(async (msg) => {
      if (msg.file && msg.file.type === 'application/pdf') {
        try {
          // Validate PDF file
          if (!msg.file.content || !msg.file.content.startsWith('data:application/pdf;base64,')) {
            throw new Error('Invalid PDF format - must be base64 data URL');
          }


          // If PDF content is provided, extract text
          if (msg.file.content) {
            const extractedText = await processPdfFile(msg.file);

            // Add the extracted text to the message content
            const pdfContent = `\n\n[PDF Document: ${msg.file.name}]\n${extractedText}`;
            return {
              ...msg,
              content: msg.content + pdfContent
            };
          } else {
            // If no content, just mention the PDF
            return {
              ...msg,
              content: msg.content + `\n\n[User uploaded a PDF document: ${msg.file.name}]`
            };
          }
        } catch (error) {
          console.error('Error processing PDF:', error);
          return {
            ...msg,
            content: msg.content + `\n\n[Error processing PDF document: ${msg.file.name}]`
          };
        }
      }
      return msg;
    }));

    // Create a new array with system message first
    const apiMessages = [universitySystemMessage, ...processedMessages];

    

    try {
      // Check if any message contains images to determine if we need vision capabilities
      const hasImages = userMessages.some(msg =>
        Array.isArray(msg.content) &&
        msg.content.some(item => item.type === 'image_url')
      );

      if (hasImages) {

        // Validate image data
        for (const msg of userMessages) {
          if (Array.isArray(msg.content)) {
            for (const item of msg.content) {
              if (item.type === 'image_url' && item.image_url && item.image_url.url) {
                // Check if it's a valid base64 data URL
                if (!item.image_url.url.startsWith('data:image/')) {
                  console.error('Invalid image format - must be base64 data URL');
                  return res.status(400).json({
                    error: 'Invalid image format',
                    message: 'Images must be in base64 data URL format'
                  });
                }
              }
            }
          }
        }
      }

      // Create the request body with the system message
      const requestBody = {
        model: 'gpt-4o', // GPT-4o supports both text and vision
        messages: apiMessages,
        temperature: 0.5, // Lower temperature for more precise, factual responses
        presence_penalty: 0.1, // Slight presence penalty to encourage comprehensive answers
        frequency_penalty: 0.1, // Slight frequency penalty to discourage repetition
        max_tokens: hasImages ? 4096 : undefined // Set max tokens for vision requests
      };

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${openaiApiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      // Get the response text first
      const responseText = await response.text();

      if (!response.ok) {
        // Try to parse the error as JSON if possible
        try {
          const errorData = JSON.parse(responseText);
          return res.status(response.status).json(errorData);
        } catch (parseError) {
          // If parsing fails, return the raw text
          return res.status(response.status).json({
            error: 'OpenAI API error',
            message: responseText
          });
        }
      }

      // Parse the successful response
      try {
        const data = JSON.parse(responseText);
        return res.json(data);
      } catch (parseError) {
        console.error('Error parsing OpenAI response:', parseError);
        return res.status(500).json({
          error: 'Failed to parse OpenAI response',
          message: 'The response from OpenAI could not be parsed as JSON'
        });
      }
    } catch (fetchError) {
      console.error('Fetch error:', fetchError);
      return res.status(500).json({
        error: 'Network error',
        message: fetchError.message
      });
    }
  } catch (error) {
    console.error('Error in chat route handler:', error);
    return res.status(500).json({
      error: 'Server error',
      message: error.message || 'An unexpected error occurred'
    });
  }
});

export default router;

