import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import rateLimiter from './middleware/rateLimiter.js';
import systemMessageMiddleware from './middleware/systemMessageMiddleware.js';
import chatRoutes from './routes/chatRoutes.js';

// Load environment variables
dotenv.config();

// Create Express app
const app = express();
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Middleware - increase payload limit for PDF uploads
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));
app.use(cors({
  origin: ['https://www.freeuniai.com', 'http://localhost:8080'],
  methods: ['GET', 'POST'],
  credentials: true
}));

// Apply rate limiting to all routes
app.use(rateLimiter);

// Apply system message middleware to ensure it's always included
app.use(systemMessageMiddleware);

// API routes
app.use('/api/chat', chatRoutes);

// Serve static files from the dist directory
app.use(express.static(path.join(__dirname, '../dist')));

// Handle all other routes by serving the index.html file
app.get('*', (_, res) => {
  res.sendFile(path.join(__dirname, '../dist/index.html'));
});

// Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
}).on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.log(`Port ${PORT} is already in use. Trying port ${PORT + 1}...`);
    // Try the next port
    const newPort = PORT + 1;
    app.listen(newPort, () => {
      console.log(`Server running on port ${newPort}`);
    });
  } else {
    console.error('Server error:', err);
  }
});

