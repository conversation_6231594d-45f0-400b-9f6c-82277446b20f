
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";

interface FeatureCardProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ title, description, icon }) => {
  return (
    <Card className="bg-gray-50 border-gray-200 h-full shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium text-gray-800 flex items-center gap-2">
          {icon}
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600 text-sm">{description}</p>
      </CardContent>
    </Card>
  );
};

const WelcomeCard = () => {
  const features: FeatureCardProps[] = [
    {
      title: "Advanced Academic Problem-Solver",
      description: "Master complex academic challenges with interactive step-by-step solutions. Get precise explanations with proper citations, visualizations, and comprehensive analysis across all university subjects."
    },
    {
      title: "Comprehensive Research & Study Assistant",
      description: "Access expert guidance for research papers, exam preparation, and complex concepts. Get help with methodology, literature reviews, data analysis, and converting theoretical knowledge into practical understanding."
    }
  ];

  return (
    <div className="py-6 animate-fade-in">
      <Card className="bg-gray-100 border-gray-200 shadow-sm mb-6">
        <CardHeader className="pb-2">
          <CardTitle className="text-center text-2xl font-bold text-gray-800">
            UniAI - Free AI for University Students
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center mb-2 text-gray-600">
            <p className="mb-2">
              Unlock your academic potential with unlimited AI assistance - optimized for university studies.
              Get help with research, assignments, and complex concepts across all disciplines.
            </p>
            <p className="text-sm text-gray-500">
              To get started, just type your question below and press send.
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {features.map((feature, index) => (
          <FeatureCard key={index} {...feature} />
        ))}
      </div>
    </div>
  );
};

export default WelcomeCard;
