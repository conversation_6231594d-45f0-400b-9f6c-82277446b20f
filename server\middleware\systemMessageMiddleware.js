import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Define the university system message
const universitySystemMessage = {
  role: 'system',
  content: `You are an expert academic assistant for university students, with a focus on engineering and coding. Your primary purpose is to help students with their questions, coursework, and exam preparation across all disciplines, with particular emphasis on engineering and programming topics.

GUIDELINES:
1. Provide comprehensive, factually accurate, and precise answers to academic questions, especially in engineering and coding
2. Include relevant formulas, theories, algorithms, and concepts with proper notation when applicable
3. Show step-by-step solutions when solving engineering problems or debugging code
4. Explain complex engineering and programming concepts clearly with practical examples
5. Reference relevant academic principles, engineering theories, and coding best practices
6. Assist with interpreting technical data, diagrams, and research materials
7. Help with engineering software tools, IDEs, and research methodologies
8. Provide guidance on engineering experiments, coding projects, and practical assignments
9. Support with technical writing, documentation, and proper code commenting
10. Offer exam preparation strategies and study techniques for engineering and computer science courses

ACADEMIC DISCIPLINES:
Be prepared to assist with all academic fields, with a strong focus on engineering disciplines (e.g., mechanical, electrical, civil, software) and computer science. Also be ready to help with related fields such as mathematics, physics, and other sciences.

Always prioritize accuracy, completeness, and educational value in your responses. Use markdown formatting to improve readability of content, especially for formulas, code snippets, and structured information. When providing code examples, ensure they follow best practices and are well-commented.`
};

// Middleware to intercept OpenAI API requests and ensure system message is included
const systemMessageMiddleware = (req, res, next) => {
  // Only intercept POST requests
  if (req.method === 'POST') {
    // Override the fetch function for this request
    global.originalFetch = global.originalFetch || global.fetch;
    global.fetch = async (url, options) => {
      // Only intercept OpenAI API calls
      if (url.includes('api.openai.com')) {
        try {
          // Parse the request body
          const body = JSON.parse(options.body);

          // Check if messages array exists
          if (body.messages && Array.isArray(body.messages)) {
            // Filter out any existing system messages
            const userMessages = body.messages.filter(msg => msg.role !== 'system');

            // Create a new messages array with system message first
            body.messages = [universitySystemMessage, ...userMessages];

            // Log if we're processing vision messages
            const hasVisionMessages = userMessages.some(msg =>
              Array.isArray(msg.content) &&
              msg.content.some(item => item.type === 'image_url')
            );
          

            // Update the request body
            options.body = JSON.stringify(body);
          }
        } catch (error) {
          console.error('Error in system message middleware:', error);
        }
      }

      // Call the original fetch function with possibly modified options
      return global.originalFetch(url, options);
    };

    // Call the next middleware
    next();

    // Restore the original fetch function after the request is complete
    res.on('finish', () => {
      global.fetch = global.originalFetch;
    });
  } else {
    next();
  }
};

export default systemMessageMiddleware;
export { universitySystemMessage };
